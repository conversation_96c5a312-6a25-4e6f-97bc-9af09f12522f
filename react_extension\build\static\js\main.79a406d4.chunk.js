(this.webpackJsonpreact_extension=this.webpackJsonpreact_extension||[]).push([[0],{26:function(e,t,n){"use strict";n.r(t);var r=n(1),c=n(0),a=n(13),s=n.n(a),o=n(3),i=n(4),j=n(6),u=n(5),h=function(e){Object(j.a)(n,e);var t=Object(u.a)(n);function n(){var e;Object(o.a)(this,n);for(var r=arguments.length,c=new Array(r),a=0;a<r;a++)c[a]=arguments[a];return(e=t.call.apply(t,[this].concat(c))).state={users:"abc"},e}return Object(i.a)(n,[{key:"render",value:function(){return Object(r.jsxs)("div",{children:[Object(r.jsx)("h2",{children:"\u6211\u662fChild\u7ec4\u4ef6"}),this.state.users.map((function(e){return Object(r.jsxs)("h4",{children:[e.name,"----",e.age]},e.id)}))]})}}]),n}(c.Component),l=function(e){Object(j.a)(n,e);var t=Object(u.a)(n);function n(){var e;Object(o.a)(this,n);for(var r=arguments.length,c=new Array(r),a=0;a<r;a++)c[a]=arguments[a];return(e=t.call.apply(t,[this].concat(c))).state={hasError:""},e}return Object(i.a)(n,[{key:"componentDidCatch",value:function(){console.log("\u6e32\u67d3\u7ec4\u4ef6\u65f6\u51fa\u9519")}},{key:"render",value:function(){return Object(r.jsxs)("div",{children:[Object(r.jsx)("h2",{children:"\u6211\u662fParent\u7ec4\u4ef6"}),this.state.hasError?Object(r.jsx)("h2",{children:"\u5f53\u524d\u7f51\u7edc\u4e0d\u7a33\u5b9a\uff0c\u7a0d\u540e\u518d\u8bd5"}):Object(r.jsx)(h,{})]})}}],[{key:"getDerivedStateFromError",value:function(e){return console.log("@@@",e),{hasError:e}}}]),n}(c.Component),b=function(e){Object(j.a)(n,e);var t=Object(u.a)(n);function n(){return Object(o.a)(this,n),t.apply(this,arguments)}return Object(i.a)(n,[{key:"render",value:function(){return Object(r.jsx)(c.Fragment,{children:Object(r.jsx)(l,{})})}}]),n}(c.Component),O=n(15);s.a.render(Object(r.jsx)(O.a,{children:Object(r.jsx)(b,{})}),document.getElementById("root"))}},[[26,1,2]]]);
//# sourceMappingURL=main.79a406d4.chunk.js.map