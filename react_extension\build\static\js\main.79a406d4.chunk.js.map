{"version": 3, "sources": ["components/8_ErrorBoundary/Child.jsx", "components/8_ErrorBoundary/Parent.jsx", "App.js", "index.js"], "names": ["Child", "state", "users", "this", "map", "userObj", "name", "age", "id", "Component", "Parent", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "error", "App", "ReactDOM", "render", "document", "getElementById"], "mappings": "yLAEqBA,E,4MACpBC,MAAQ,CAMPC,MAAM,O,uDAGN,OACC,gCACC,+DAECC,KAAKF,MAAMC,MAAME,KAAI,SAACC,GACrB,OAAO,+BAAsBA,EAAQC,KAA9B,OAAwCD,EAAQE,MAAvCF,EAAQG,c,GAfKC,aCCdC,E,4MAEpBT,MAAQ,CACPU,SAAS,I,kEAUTC,QAAQC,IAAI,gD,+BAIZ,OACC,gCACC,gEACCV,KAAKF,MAAMU,SAAW,0GAAwB,cAAC,EAAD,U,gDAblBG,GAE/B,OADAF,QAAQC,IAAI,MAAMC,GACX,CAACH,SAASG,O,GATiBL,aCAfM,E,uKAEnB,OACC,cAAC,WAAD,UACC,cAAC,EAAD,U,GAJ6BN,a,QCEjCO,IAASC,OACR,cAAC,IAAD,UACC,cAAC,EAAD,MAEDC,SAASC,eAAe,W", "file": "static/js/main.79a406d4.chunk.js", "sourcesContent": ["import React, { Component } from 'react'\r\n\r\nexport default class Child extends Component {\r\n\tstate = {\r\n\t\t/* users:[\r\n\t\t\t{id:'001',name:'tom',age:18},\r\n\t\t\t{id:'002',name:'jack',age:19},\r\n\t\t\t{id:'003',name:'pei<PERSON>',age:20},\r\n\t\t] */\r\n\t\tusers:'abc'\r\n\t}\r\n\trender() {\r\n\t\treturn (\r\n\t\t\t<div>\r\n\t\t\t\t<h2>我是Child组件</h2>\r\n\t\t\t\t{\r\n\t\t\t\t\tthis.state.users.map((userObj)=>{\r\n\t\t\t\t\t\treturn <h4 key={userObj.id}>{userObj.name}----{userObj.age}</h4>\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n}", "import React, { Component } from 'react'\r\nimport Child from './Child'\r\n\r\nexport default class Parent extends Component {\r\n\r\n\tstate = {\r\n\t\thasError:'' //用于标识子组件是否产生错误\r\n\t}\r\n\r\n\t//当Parent的子组件出现报错时候，会触发getDerivedStateFromError调用，并携带错误信息\r\n\tstatic getDerivedStateFromError(error){\r\n\t\tconsole.log('@@@',error);\r\n\t\treturn {hasError:error}\r\n\t}\r\n\r\n\tcomponentDidCatch(){\r\n\t\tconsole.log('渲染组件时出错');\r\n\t}\r\n\r\n\trender() {\r\n\t\treturn (\r\n\t\t\t<div>\r\n\t\t\t\t<h2>我是Parent组件</h2>\r\n\t\t\t\t{this.state.hasError ? <h2>当前网络不稳定，稍后再试</h2> : <Child/>}\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n}\r\n", "import React, { Component,Fragment } from 'react'\r\nimport Demo from './components/8_ErrorBoundary/Parent'\r\n\r\nexport default class App extends Component {\r\n\trender() {\r\n\t\treturn (\r\n\t\t\t<Fragment>\r\n\t\t\t\t<Demo/>\r\n\t\t\t</Fragment>\r\n\t\t)\r\n\t}\r\n}\r\n", "import React from 'react'\r\nimport ReactDOM from 'react-dom'\r\nimport App from './App'\r\nimport {BrowserRouter} from 'react-router-dom'\r\n\r\nReactDOM.render(\r\n\t<BrowserRouter>\r\n\t\t<App/>\r\n\t</BrowserRouter>,\r\n\tdocument.getElementById('root'))"], "sourceRoot": ""}